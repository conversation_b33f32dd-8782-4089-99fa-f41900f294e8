// KMS Member Dashboard JavaScript

// Global variables
let currentMode = 'simple';
let selectedComponents = {};
let serviceOrders = {};
let walletData = {};

// Initialize when DOM is loaded
document.addEventListener('DOMContentLoaded', function() {
    initializeMemberDashboard();
});

// Main initialization function
function initializeMemberDashboard() {
    loadWalletData();
    loadServicePrices();
    loadPCComponents();
    loadAffiliateData();
    setupEventListeners();
    startHeartbeat();
}

// Setup event listeners
function setupEventListeners() {
    // Wallet action buttons
    const depositBtn = document.getElementById('deposit-btn');
    const withdrawBtn = document.getElementById('withdraw-btn');
    const transferBtn = document.getElementById('transfer-btn');
    
    if (depositBtn) depositBtn.addEventListener('click', showDepositModal);
    if (withdrawBtn) withdrawBtn.addEventListener('click', showWithdrawModal);
    if (transferBtn) transferBtn.addEventListener('click', showTransferModal);
    
    // PC Builder mode buttons
    const modeButtons = document.querySelectorAll('.mode-btn');
    modeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            switchPCBuilderMode(this.dataset.mode);
        });
    });
    
    // Service order buttons
    const orderButtons = document.querySelectorAll('.create-order-btn');
    orderButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            createServiceOrder(this.dataset.category);
        });
    });

    // Affiliate action buttons
    const withdrawCommissionBtn = document.getElementById('withdraw-commission-btn');
    const transferToCreditBtn = document.getElementById('transfer-to-credit-btn');

    if (withdrawCommissionBtn) {
        withdrawCommissionBtn.addEventListener('click', withdrawCommission);
    }
    if (transferToCreditBtn) {
        transferToCreditBtn.addEventListener('click', transferCommissionToCredit);
    }

    // PC Builder order button
    const createPCOrderBtn = document.getElementById('create-pc-order');
    if (createPCOrderBtn) {
        createPCOrderBtn.addEventListener('click', function() {
            if (!selectedComponents || Object.keys(selectedComponents).length === 0) {
                showNotification('Please select PC components first', 'error');
                return;
            }

            // Get notes
            const notes = document.getElementById('pc-notes').value;

            // Check if using KMS Credit
            const useCredit = document.getElementById('use-credit-pc').checked;

            // Get total
            let total = 0;
            if (currentMode === 'simple' && selectedComponents.total) {
                total = selectedComponents.total;
            } else if (currentMode === 'detailed' && selectedComponents.components) {
                total = Object.values(selectedComponents.components).reduce((sum, comp) => sum + comp.price, 0);
                total += 100; // System service
            } else if (currentMode === 'prebuilt' && selectedComponents.total) {
                total = selectedComponents.total;
                total += 100; // System service
            }

            // Create order object
            const order = {
                type: 'pc_build',
                mode: currentMode,
                components: selectedComponents,
                notes: notes,
                useCredit: useCredit,
                total: total,
                timestamp: new Date().toISOString()
            };

            // In a real implementation, this would be sent to the server
            console.log('Creating PC order:', order);

            // Show success notification
            showNotification('PC Build order created successfully! Our team will contact you soon.', 'success');

            // Reset form
            document.getElementById('pc-notes').value = '';
            document.getElementById('use-credit-pc').checked = false;
            selectedComponents = {};

            // Reset selections
            document.querySelectorAll('.pc-option-card, .component-option, .prebuilt-config-card').forEach(el => {
                el.classList.remove('selected');
            });

            // Update total
            updatePCTotal();
        });
    }
    
    // Modal close buttons
    const closeButtons = document.querySelectorAll('.close');
    closeButtons.forEach(btn => {
        btn.addEventListener('click', function() {
            closeModal(this.closest('.modal'));
        });
    });
}

// Wallet Functions
async function loadWalletData() {
    try {
        const response = await fetch('KMS_Wallet/KMS_PHP/KMS_credit_wallet.php?action=get_balance', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            walletData = data.data;
            updateWalletDisplay();
        } else {
            console.error('Failed to load wallet data:', data.message);
        }
    } catch (error) {
        console.error('Error loading wallet data:', error);
    }
}

function updateWalletDisplay() {
    const balanceElement = document.getElementById('walletBalance');
    const totalDepositedElement = document.getElementById('totalDeposited');
    const totalSpentElement = document.getElementById('totalSpent');
    const frozenBalanceElement = document.getElementById('frozenBalance');
    
    if (balanceElement) balanceElement.textContent = `$${parseFloat(walletData.balance || 0).toFixed(2)}`;
    if (totalDepositedElement) totalDepositedElement.textContent = `$${parseFloat(walletData.total_deposited || 0).toFixed(2)}`;
    if (totalSpentElement) totalSpentElement.textContent = `$${parseFloat(walletData.total_spent || 0).toFixed(2)}`;
    if (frozenBalanceElement) frozenBalanceElement.textContent = `$${parseFloat(walletData.frozen_balance || 0).toFixed(2)}`;
}

// Service Functions
async function loadServicePrices() {
    try {
        const response = await fetch('KMS_Orders/KMS_PHP/KMS_service_prices_api.php?action=get_prices', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            displayServicePrices(data.data);
        } else {
            console.error('Failed to load service prices:', data.message);
        }
    } catch (error) {
        console.error('Error loading service prices:', error);
    }
}

function displayServicePrices(prices) {
    const optimizeContainer = document.getElementById('optimize-services');
    const printContainer = document.getElementById('print-services');
    
    if (optimizeContainer) {
        const optimizeServices = prices.filter(p => p.service_category === 'optimize');
        optimizeContainer.innerHTML = generateServiceHTML(optimizeServices, 'optimize');
    }
    
    if (printContainer) {
        const printServices = prices.filter(p => p.service_category === 'print');
        printContainer.innerHTML = generateServiceHTML(printServices, 'print');
    }
}

function generateServiceHTML(services, category) {
    return services.map(service => `
        <div class="service-item" data-service-id="${service.id}">
            <div class="service-info">
                <div class="service-name">${service.item_name}</div>
                <div class="service-description">${service.description}</div>
                <div class="service-price">$${parseFloat(service.base_price).toFixed(2)} / ${service.unit}</div>
            </div>
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="changeQuantity('${service.id}', -1)">-</button>
                <input type="number" class="quantity-input" id="qty-${service.id}" value="0" min="0" onchange="updateServiceOrder('${service.id}', this.value)">
                <button class="quantity-btn" onclick="changeQuantity('${service.id}', 1)">+</button>
            </div>
        </div>
    `).join('');
}

function changeQuantity(serviceId, change) {
    const input = document.getElementById(`qty-${serviceId}`);
    const currentValue = parseInt(input.value) || 0;
    const newValue = Math.max(0, currentValue + change);
    input.value = newValue;
    updateServiceOrder(serviceId, newValue);
}

function updateServiceOrder(serviceId, quantity) {
    if (quantity > 0) {
        serviceOrders[serviceId] = parseInt(quantity);
    } else {
        delete serviceOrders[serviceId];
    }
    updateOrderSummary();
}

function updateOrderSummary() {
    // Implementation for updating order summary
    console.log('Current service orders:', serviceOrders);
}

// PC Builder Functions
async function loadPCComponents() {
    // Load Simple Mode options
    loadSimpleModeOptions();

    // Load Detailed Mode components
    loadDetailedModeComponents();

    // Load Pre-built configurations
    loadPrebuiltConfigurations();
}

function loadSimpleModeOptions() {
    const simpleContainer = document.getElementById('simple-mode-options');
    if (!simpleContainer) return;

    const simpleModes = [
        {
            title: 'Gaming Beast',
            description: 'High-end gaming PC with RTX 5080/5090',
            price: '$3500 - $5000',
            specs: ['Intel i7/i9 or AMD Ryzen 7/9', 'RTX 5080/5090', '32GB DDR5', '2TB NVMe SSD'],
            color: 'RGB Multi-color'
        },
        {
            title: 'Content Creator',
            description: 'Perfect for video editing and streaming',
            price: '$2500 - $3500',
            specs: ['Intel i7 or AMD Ryzen 7', 'RTX 5070/5080', '32GB DDR5', '1TB NVMe SSD'],
            color: 'White/Black'
        },
        {
            title: 'Office Pro',
            description: 'Professional workstation for productivity',
            price: '$1500 - $2500',
            specs: ['Intel i5 or AMD Ryzen 5', 'RTX 5070', '16GB DDR5', '1TB NVMe SSD'],
            color: 'Black/Silver'
        }
    ];

    simpleContainer.innerHTML = simpleModes.map(mode => `
        <div class="pc-option-card" data-mode="${mode.title.toLowerCase().replace(' ', '-')}">
            <h4>${mode.title}</h4>
            <p>${mode.description}</p>
            <div class="price-range">${mode.price}</div>
            <ul class="specs-list">
                ${mode.specs.map(spec => `<li>${spec}</li>`).join('')}
            </ul>
            <div class="color-option">Color: ${mode.color}</div>
            <button class="btn-primary select-option-btn" onclick="selectSimpleMode('${mode.title}')">
                Select This Build
            </button>
        </div>
    `).join('');
}

function loadDetailedModeComponents() {
    const detailedContainer = document.getElementById('detailed-mode-components');
    if (!detailedContainer) return;

    const componentCategories = [
        {
            category: 'CPU',
            options: [
                { name: 'Intel Core i9-285K', price: 760, description: 'Latest flagship processor' },
                { name: 'Intel Core i5-265K', price: 380, description: 'Great performance/price ratio' },
                { name: 'AMD Ryzen 7 7800X3D', price: 481, description: 'Gaming powerhouse' },
                { name: 'AMD Ryzen 9 9800X3D', price: 608, description: 'Ultimate gaming CPU' },
                { name: 'AMD Ryzen 9 9950X3D', price: 886, description: 'Flagship workstation CPU' }
            ]
        },
        {
            category: 'GPU',
            options: [
                { name: 'RTX 5070', price: 899, description: '1440p gaming excellence' },
                { name: 'RTX 5080', price: 1299, description: '4K gaming ready' },
                { name: 'RTX 5090', price: 1999, description: 'Ultimate 4K gaming' }
            ]
        },
        {
            category: 'RAM',
            options: [
                { name: '32GB DDR5-6000', price: 299, description: 'High-speed gaming memory' },
                { name: '64GB DDR5-6000', price: 599, description: 'Content creation powerhouse' },
                { name: '128GB DDR5-5600', price: 1199, description: 'Workstation grade memory' }
            ]
        },
        {
            category: 'Storage',
            options: [
                { name: '2TB NVMe Gen4', price: 299, description: 'Ultra-fast storage' },
                { name: '4TB NVMe Gen4', price: 599, description: 'Massive fast storage' },
                { name: '8TB NVMe Gen4', price: 1199, description: 'Enterprise level storage' }
            ]
        }
    ];

    detailedContainer.innerHTML = componentCategories.map(cat => `
        <div class="component-category">
            <h4>${cat.category}</h4>
            <div class="component-options">
                ${cat.options.map(option => `
                    <div class="component-option" data-category="${cat.category}" data-name="${option.name}" data-price="${option.price}">
                        <div class="component-name">${option.name}</div>
                        <div class="component-price">$${option.price}</div>
                        <div class="component-description">${option.description}</div>
                        <button class="btn-secondary select-component-btn" onclick="selectComponent('${cat.category}', '${option.name}', ${option.price})">
                            Select
                        </button>
                    </div>
                `).join('')}
            </div>
        </div>
    `).join('');
}

function loadPrebuiltConfigurations() {
    const prebuiltContainer = document.getElementById('prebuilt-configs');
    if (!prebuiltContainer) return;

    const prebuiltConfigs = [
        {
            name: 'KMS Gaming Pro',
            price: 3999,
            specs: ['Intel i7-285K', 'RTX 5080', '32GB DDR5', '2TB NVMe', 'RGB Lighting'],
            description: 'Perfect balance of performance and value'
        },
        {
            name: 'KMS Creator Station',
            price: 4999,
            specs: ['AMD Ryzen 9 9800X3D', 'RTX 5090', '64GB DDR5', '4TB NVMe', 'Professional Cooling'],
            description: 'Ultimate content creation machine'
        },
        {
            name: 'KMS Office Elite',
            price: 2499,
            specs: ['Intel i5-265K', 'RTX 5070', '32GB DDR5', '2TB NVMe', 'Silent Operation'],
            description: 'Professional workstation for business'
        }
    ];

    prebuiltContainer.innerHTML = prebuiltConfigs.map(config => `
        <div class="prebuilt-config-card">
            <h4>${config.name}</h4>
            <div class="config-price">$${config.price}</div>
            <div class="config-description">${config.description}</div>
            <ul class="config-specs">
                ${config.specs.map(spec => `<li>${spec}</li>`).join('')}
            </ul>
            <button class="btn-primary select-config-btn" onclick="selectPrebuiltConfig('${config.name}', ${config.price})">
                Select Configuration
            </button>
        </div>
    `).join('');
}

function switchPCBuilderMode(mode) {
    currentMode = mode;

    // Update active button
    document.querySelectorAll('.mode-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    document.querySelector(`[data-mode="${mode}"]`).classList.add('active');

    // Show/hide appropriate sections
    const sections = document.querySelectorAll('.pc-mode-section');
    sections.forEach(section => {
        section.style.display = section.dataset.mode === mode ? 'block' : 'none';
    });

    // Update total based on mode
    updatePCTotal();
}

// PC Builder selection functions
function selectSimpleMode(modeName) {
    selectedComponents = { mode: modeName };

    const prices = {
        'gaming-beast': 4250,
        'content-creator': 3000,
        'office-pro': 2000
    };

    const modeKey = modeName.toLowerCase().replace(' ', '-');
    selectedComponents.total = prices[modeKey] || 0;

    // Highlight selected option
    document.querySelectorAll('.pc-option-card').forEach(card => {
        card.classList.remove('selected');
    });
    document.querySelector(`[data-mode="${modeKey}"]`).classList.add('selected');

    updatePCTotal();
    showNotification(`Selected ${modeName} configuration`, 'success');
}

function selectComponent(category, name, price) {
    if (!selectedComponents.components) {
        selectedComponents.components = {};
    }

    selectedComponents.components[category] = { name, price };

    // Highlight selected component
    document.querySelectorAll(`[data-category="${category}"] .component-option`).forEach(option => {
        option.classList.remove('selected');
    });
    document.querySelector(`[data-category="${category}"][data-name="${name}"]`).classList.add('selected');

    updatePCTotal();
    showNotification(`Selected ${name} for ${category}`, 'success');
}

function selectPrebuiltConfig(configName, price) {
    selectedComponents = {
        prebuilt: configName,
        total: price
    };

    // Highlight selected config
    document.querySelectorAll('.prebuilt-config-card').forEach(card => {
        card.classList.remove('selected');
    });
    event.target.closest('.prebuilt-config-card').classList.add('selected');

    updatePCTotal();
    showNotification(`Selected ${configName} configuration`, 'success');
}

function updatePCTotal() {
    let total = 0;

    if (currentMode === 'simple' && selectedComponents.total) {
        total = selectedComponents.total;
    } else if (currentMode === 'detailed' && selectedComponents.components) {
        total = Object.values(selectedComponents.components).reduce((sum, comp) => sum + comp.price, 0);
        // Add mandatory $100 system service
        total += 100;
    } else if (currentMode === 'prebuilt' && selectedComponents.total) {
        total = selectedComponents.total;
        // Add mandatory $100 system service
        total += 100;
    }

    const totalElement = document.getElementById('pc-total');
    if (totalElement) {
        totalElement.textContent = `Total: $${total.toFixed(2)}`;
    }
}

// Affiliate Functions
async function loadAffiliateData() {
    try {
        const response = await fetch('KMS_Affiliate/KMS_PHP/KMS_affiliate_api.php?action=get_affiliate_data', {
            method: 'GET',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
        
        const data = await response.json();
        if (data.success) {
            displayAffiliateData(data.data);
        } else {
            console.error('Failed to load affiliate data:', data.message);
        }
    } catch (error) {
        console.error('Error loading affiliate data:', error);
    }
}

function displayAffiliateData(data) {
    const codeElement = document.getElementById('affiliate-code');
    const statsContainer = document.getElementById('affiliate-stats');
    
    if (codeElement && data.affiliate_code) {
        codeElement.textContent = data.affiliate_code;
    }
    
    if (statsContainer && data.stats) {
        updateAffiliateStats(data.stats);
    }
}

function updateAffiliateStats(stats) {
    const elements = {
        'total-referrals': stats.total_referrals || 0,
        'confirmed-referrals': stats.confirmed_referrals || 0,
        'pending-referrals': stats.pending_referrals || 0,
        'total-commissions': `$${parseFloat(stats.total_commissions || 0).toFixed(2)}`
    };
    
    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) element.textContent = value;
    });
}

// Modal Functions
function showDepositModal() {
    const modal = document.getElementById('deposit-modal');
    if (modal) modal.style.display = 'block';
}

function showWithdrawModal() {
    const modal = document.getElementById('withdraw-modal');
    if (modal) modal.style.display = 'block';
}

function showTransferModal() {
    const modal = document.getElementById('transfer-modal');
    if (modal) modal.style.display = 'block';
}

function closeModal(modal) {
    if (typeof modal === 'string') {
        // If modal is a string ID
        const modalElement = document.getElementById(modal);
        if (modalElement) {
            modalElement.style.display = 'none';
            modalElement.remove();
        }
    } else if (modal) {
        // If modal is a DOM element
        modal.style.display = 'none';
    }
}

// Order Functions
async function createServiceOrder(category) {
    const orderItems = Object.entries(serviceOrders).map(([serviceId, quantity]) => ({
        service_id: serviceId,
        quantity: quantity
    }));
    
    if (orderItems.length === 0) {
        alert('Please select at least one service item.');
        return;
    }
    
    const useCredit = document.getElementById('use-credit')?.checked || false;
    const notes = document.getElementById('order-notes')?.value || '';
    
    try {
        const formData = new FormData();
        formData.append('action', 'create_order');
        formData.append('service_category', category);
        formData.append('order_items', JSON.stringify(orderItems));
        formData.append('use_credit', useCredit ? '1' : '0');
        formData.append('notes', notes);
        
        const response = await fetch('KMS_Orders/KMS_PHP/KMS_service_orders_api.php', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            },
            body: formData
        });
        
        const data = await response.json();
        if (data.success) {
            alert('Order created successfully!');
            // Reset form
            serviceOrders = {};
            document.querySelectorAll('.quantity-input').forEach(input => input.value = '0');
            updateOrderSummary();
            loadWalletData(); // Refresh wallet data
        } else {
            alert('Failed to create order: ' + data.message);
        }
    } catch (error) {
        console.error('Error creating order:', error);
        alert('An error occurred while creating the order.');
    }
}

// Heartbeat function to keep user online
function startHeartbeat() {
    // Send heartbeat every minute
    setInterval(sendHeartbeat, 60000);
}

async function sendHeartbeat() {
    try {
        await fetch('../KMS_Index/KMS_UserTracking/KMS_PHP/KMS_heartbeat.php', {
            method: 'POST',
            credentials: 'same-origin',
            headers: {
                'Accept': 'application/json',
                'X-Requested-With': 'XMLHttpRequest'
            }
        });
    } catch (error) {
        console.error('Heartbeat failed:', error);
    }
}

// Utility Functions
function formatCurrency(amount) {
    return `$${parseFloat(amount || 0).toFixed(2)}`;
}

function showNotification(message, type = 'info') {
    // Simple notification system
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        padding: 15px 20px;
        background: ${type === 'error' ? '#f44336' : type === 'success' ? '#4CAF50' : '#2196F3'};
        color: white;
        border-radius: 5px;
        z-index: 10000;
        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
    `;
    
    document.body.appendChild(notification);
    
    setTimeout(() => {
        notification.remove();
    }, 5000);
}

// Copy to clipboard function
function copyToClipboard(text) {
    navigator.clipboard.writeText(text).then(() => {
        showNotification('Copied to clipboard!', 'success');
    }).catch(err => {
        console.error('Failed to copy:', err);
        showNotification('Failed to copy to clipboard', 'error');
    });
}

// Page unload handler for offline tracking
window.addEventListener('beforeunload', function() {
    navigator.sendBeacon('../KMS_Index/KMS_UserTracking/KMS_PHP/KMS_log_offline.php');
});

// Scroll to section function
function scrollToSection(sectionId) {
    const element = document.getElementById(sectionId);
    if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
    }
}

// Affiliate commission withdrawal
function withdrawCommission() {
    const totalCommissions = parseFloat(document.getElementById('total-commissions').textContent.replace('$', ''));

    if (totalCommissions <= 0) {
        showNotification('No commission available for withdrawal', 'error');
        return;
    }

    if (totalCommissions < 50) {
        showNotification('Minimum withdrawal amount is $50', 'error');
        return;
    }

    // Show withdrawal modal
    showWithdrawCommissionModal(totalCommissions);
}

// Transfer commission to KMS Credit
function transferCommissionToCredit() {
    const totalCommissions = parseFloat(document.getElementById('total-commissions').textContent.replace('$', ''));

    if (totalCommissions <= 0) {
        showNotification('No commission available for transfer', 'error');
        return;
    }

    // Confirm transfer
    if (confirm(`Transfer $${totalCommissions.toFixed(2)} commission to KMS Credit?`)) {
        // In real implementation, this would call the server
        const currentBalance = parseFloat(document.getElementById('walletBalance').textContent.replace('$', ''));
        const newBalance = currentBalance + totalCommissions;

        // Update wallet balance
        document.getElementById('walletBalance').textContent = `$${newBalance.toFixed(2)}`;

        // Reset commission
        document.getElementById('total-commissions').textContent = '$0.00';

        showNotification(`Successfully transferred $${totalCommissions.toFixed(2)} to KMS Credit`, 'success');

        // Update wallet data
        loadWalletData();
    }
}

// Show withdraw commission modal
function showWithdrawCommissionModal(amount) {
    // Create modal HTML
    const modalHTML = `
        <div class="modal" id="withdraw-commission-modal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
            <div class="modal-content withdraw-commission-modal">
                <span class="close" onclick="closeModal('withdraw-commission-modal')">&times;</span>
                <h3>💰 Withdraw Commission</h3>
                <div class="withdraw-info">
                    <p><strong>Available Commission:</strong> $${amount.toFixed(2)}</p>
                    <p><strong>Minimum Withdrawal:</strong> $50.00</p>
                </div>

                <div class="form-group">
                    <label for="withdraw-amount">Withdrawal Amount:</label>
                    <input type="number" id="withdraw-amount" min="50" max="${amount}" step="0.01" value="${amount}" style="background-color: #ffc000; color: white;">
                </div>

                <div class="form-group">
                    <label>Payment Method:</label>
                    <div class="payment-methods">
                        <button class="payment-method-btn" data-method="paypal">PayPal</button>
                        <button class="payment-method-btn" data-method="stripe">Stripe</button>
                        <button class="payment-method-btn" data-method="bank">Bank Transfer</button>
                        <button class="payment-method-btn" data-method="venmo">Venmo</button>
                        <button class="payment-method-btn" data-method="zelle">Zelle</button>
                    </div>
                </div>

                <div class="form-group" id="payment-details" style="display: none;">
                    <label for="payment-info">Payment Details:</label>
                    <input type="text" id="payment-info" placeholder="Enter payment details">
                </div>

                <div class="modal-actions">
                    <button class="btn-primary" onclick="processCommissionWithdrawal()">Submit Withdrawal</button>
                    <button class="btn-secondary" onclick="closeModal('withdraw-commission-modal')">Cancel</button>
                </div>
            </div>
        </div>
    `;

    // Add modal to page
    document.body.insertAdjacentHTML('beforeend', modalHTML);

    // Show modal
    document.getElementById('withdraw-commission-modal').style.display = 'block';

    // Setup payment method selection
    document.querySelectorAll('.payment-method-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            // Remove active class from all buttons
            document.querySelectorAll('.payment-method-btn').forEach(b => b.classList.remove('active'));

            // Add active class to clicked button
            this.classList.add('active');

            // Show payment details input
            const paymentDetails = document.getElementById('payment-details');
            const paymentInfo = document.getElementById('payment-info');

            paymentDetails.style.display = 'block';

            // Update placeholder based on method
            const method = this.dataset.method;
            const placeholders = {
                paypal: 'Enter PayPal email address',
                stripe: 'Enter Stripe account details',
                bank: 'Enter bank account details',
                venmo: 'Enter Venmo username',
                zelle: 'Enter Zelle email or phone'
            };

            paymentInfo.placeholder = placeholders[method] || 'Enter payment details';
        });
    });
}

// Process commission withdrawal
function processCommissionWithdrawal() {
    const amount = parseFloat(document.getElementById('withdraw-amount').value);
    const selectedMethod = document.querySelector('.payment-method-btn.active');
    const paymentInfo = document.getElementById('payment-info').value;

    if (!selectedMethod) {
        showNotification('Please select a payment method', 'error');
        return;
    }

    if (!paymentInfo.trim()) {
        showNotification('Please enter payment details', 'error');
        return;
    }

    if (amount < 50) {
        showNotification('Minimum withdrawal amount is $50', 'error');
        return;
    }

    // In real implementation, this would be sent to server for processing
    const withdrawalRequest = {
        amount: amount,
        method: selectedMethod.dataset.method,
        paymentInfo: paymentInfo,
        timestamp: new Date().toISOString(),
        status: 'pending'
    };

    console.log('Commission withdrawal request:', withdrawalRequest);

    // Show success message in modal
    const modal = document.getElementById('withdraw-commission-modal');
    if (modal) {
        const modalContent = modal.querySelector('.modal-content');
        modalContent.innerHTML = `
            <h3 style="color: #00ff00;">✅ Withdrawal Submitted</h3>
            <p>Your withdrawal request for $${amount.toFixed(2)} has been submitted successfully.</p>
            <p>Processing time: 3-5 business days.</p>
        `;

        // Auto close after 3 seconds
        setTimeout(() => {
            closeModal('withdraw-commission-modal');
        }, 3000);
    }

    // Update commission balance (subtract withdrawn amount)
    const currentCommission = parseFloat(document.getElementById('total-commissions').textContent.replace('$', ''));
    const newCommission = currentCommission - amount;
    document.getElementById('total-commissions').textContent = `$${newCommission.toFixed(2)}`;
}

// Generate affiliate code
function generateAffiliateCode() {
    const username = document.getElementById('welcome-msg').textContent.split(' ')[2]; // Extract username
    const timestamp = Date.now();
    const randomStr = Math.random().toString(36).substring(2, 8).toUpperCase();
    return `${username}_${randomStr}_${timestamp.toString().slice(-6)}`;
}

// Load affiliate data with real functionality
function loadAffiliateData() {
    // Generate or load affiliate code
    const affiliateCodeElement = document.getElementById('affiliate-code');
    if (affiliateCodeElement) {
        // Check if user already has an affiliate code
        let affiliateCode = localStorage.getItem('affiliateCode');
        if (!affiliateCode) {
            affiliateCode = generateAffiliateCode();
            localStorage.setItem('affiliateCode', affiliateCode);
        }
        affiliateCodeElement.textContent = affiliateCode;
    }

    // Load affiliate stats (placeholder for now)
    updateAffiliateStats({
        totalReferrals: 0,
        confirmedReferrals: 0,
        pendingReferrals: 0,
        totalCommissions: 0.00
    });
}

// Update affiliate statistics
function updateAffiliateStats(stats) {
    const elements = {
        'total-referrals': stats.totalReferrals,
        'confirmed-referrals': stats.confirmedReferrals,
        'pending-referrals': stats.pendingReferrals,
        'total-commissions': `$${stats.totalCommissions.toFixed(2)}`
    };

    Object.entries(elements).forEach(([id, value]) => {
        const element = document.getElementById(id);
        if (element) {
            element.textContent = value;
        }
    });
}

// Close modal function (removed duplicate - using the one above)

// Enhanced notification system
function showNotification(message, type = 'info') {
    // Remove existing notifications
    const existingNotifications = document.querySelectorAll('.notification');
    existingNotifications.forEach(n => n.remove());

    // Create notification element
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <span class="notification-message">${message}</span>
        <button class="notification-close" onclick="this.parentElement.remove()">&times;</button>
    `;

    // Add to page
    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentElement) {
            notification.remove();
        }
    }, 5000);
}

// Simulate affiliate earnings (for demo purposes)
function simulateAffiliateEarnings() {
    // This would normally come from server data
    const mockStats = {
        totalReferrals: Math.floor(Math.random() * 10) + 1,
        confirmedReferrals: Math.floor(Math.random() * 5) + 1,
        pendingReferrals: Math.floor(Math.random() * 3),
        totalCommissions: (Math.random() * 200) + 50 // Random amount between $50-$250
    };

    updateAffiliateStats(mockStats);
}
