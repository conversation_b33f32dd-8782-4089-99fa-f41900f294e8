<?php
/**
 * KMS Member Dashboard - Streamlined Version
 * Main member dashboard with all functionality
 */

// Error reporting and session management
ini_set('display_errors', 1);
error_reporting(E_ALL);
session_start();

// Include required files
$base_path = dirname(dirname(__DIR__));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Language' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_language.php';

// Authentication check
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    header('Location: ../../index.php');
    exit;
}

// Store session ID for JavaScript
$session_id = session_id();
$username = htmlspecialchars($_SESSION["username"]);
?>
<!DOCTYPE html>
<html lang="<?= $lang ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?= t('member_page_title') ?></title>
    
    <!-- External CSS -->
    <link rel="stylesheet" href="KMS_CSS/KMS_member.css">
    <link rel="stylesheet" href="KMS_Wallet/KMS_CSS/KMS_wallet.css">
    <link rel="stylesheet" href="../KMS_Index/KMS_Authentication/KMS_CSS/KMS_custom-modal.css">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="../../Favicon/favicon.ico">
    <link rel="icon" type="image/png" sizes="16x16" href="../../Favicon/KMS_Logo_16.png">
    <link rel="icon" type="image/png" sizes="32x32" href="../../Favicon/KMS_Logo_32.png">
    <link rel="icon" type="image/png" sizes="96x96" href="../../Favicon/KMS_Logo_96.png">
    <link rel="icon" type="image/png" sizes="152x152" href="../../Favicon/KMS_Logo_152.png">
    <link rel="icon" type="image/png" sizes="512x512" href="../../Favicon/KMS_Logo_512.png">
    
    <script>const SESSION_ID = '<?= $session_id ?>';</script>
</head>
<body>
    <!-- Top Right Controls -->
    <div class="top-controls">
        <button class="btn-settings" onclick="window.location.href='KMS_account_settings.php'">⚙️ Account Settings</button>
        <button class="btn-logout" onclick="window.location.href='../KMS_Index/KMS_Authentication/KMS_PHP/KMS_logout.php'">🚪 Logout</button>
    </div>

    <div class="container">
        <!-- Welcome Message -->
        <h1 id="welcome-msg"><?= str_replace('{username}', $username, t('welcome_message')) ?></h1>

        <!-- KMS Credit Wallet Section -->
        <div class="wallet-section" id="kms-credit">
            <h2>💰 KMS Credit Wallet</h2>
            <div class="wallet-card">
                <div class="wallet-balance">
                    <h3>Available Balance</h3>
                    <div class="balance-amount" id="walletBalance">$0.00</div>
                </div>

                <div class="wallet-stats">
                    <div class="stat-item">
                        <h4>Total Deposited</h4>
                        <div class="stat-value" id="totalDeposited">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Total Spent</h4>
                        <div class="stat-value" id="totalSpent">$0.00</div>
                    </div>
                    <div class="stat-item">
                        <h4>Frozen Balance</h4>
                        <div class="stat-value" id="frozenBalance">$0.00</div>
                    </div>
                </div>

                <div class="wallet-actions">
                    <button id="deposit-btn" class="btn-deposit">💳 Deposit</button>
                    <button id="withdraw-btn" class="btn-withdraw">💰 Withdraw</button>
                    <button id="transfer-btn" class="btn-transfer">🔄 Transfer</button>
                </div>
            </div>
        </div>

        <!-- Affiliate System Section -->
        <div class="affiliate-section" id="affiliate-system">
            <h2>🤝 Affiliate System</h2>

            <div class="affiliate-code-display">
                <h3>Your Affiliate Code</h3>
                <div class="affiliate-code" id="affiliate-code">Loading...</div>
                <button class="btn-primary" onclick="copyToClipboard(document.getElementById('affiliate-code').textContent)">
                    📋 Copy Code
                </button>
            </div>

            <div class="referral-stats" id="affiliate-stats">
                <div class="referral-stat-card">
                    <h4>Total Referrals</h4>
                    <div class="stat-value" id="total-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Confirmed Referrals</h4>
                    <div class="stat-value" id="confirmed-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Pending Referrals</h4>
                    <div class="stat-value" id="pending-referrals">0</div>
                </div>
                <div class="referral-stat-card">
                    <h4>Total Commissions</h4>
                    <div class="stat-value" id="total-commissions">$0.00</div>
                </div>
            </div>

            <div class="affiliate-actions">
                <button class="btn-primary" id="withdraw-commission-btn">💰 Withdraw Commission</button>
                <button class="btn-secondary" id="transfer-to-credit-btn">🔄 Transfer to KMS Credit</button>
            </div>
        </div>

        <!-- PC Builder Section -->
        <div class="pc-builder-section" id="pc-builder">
            <h2>🖥️ PC Builder</h2>

            <div class="mode-selector">
                <button class="mode-btn active" data-mode="simple">Simple Mode</button>
                <button class="mode-btn" data-mode="detailed">Detailed Mode</button>
                <button class="mode-btn" data-mode="prebuilt">Pre-built PCs</button>
            </div>

            <!-- Simple Mode -->
            <div class="pc-mode-section" data-mode="simple">
                <div class="simple-mode-steps">
                    <!-- Step 1: Case Color -->
                    <div class="step-container" id="step-color">
                        <h3>Step 1: Choose Case Color</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="white" onclick="selectSimpleOption('color', 'white', this)">
                                <div class="option-icon">⚪</div>
                                <div class="option-name">White</div>
                            </div>
                            <div class="option-btn" data-value="black" onclick="selectSimpleOption('color', 'black', this)">
                                <div class="option-icon">⚫</div>
                                <div class="option-name">Black</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 2: Case Size -->
                    <div class="step-container disabled" id="step-size">
                        <h3>Step 2: Choose Case Size</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="small" data-price="200" onclick="selectSimpleOption('size', 'small', this)">
                                <div class="option-icon">📦</div>
                                <div class="option-name">Small</div>
                                <div class="option-price">$200</div>
                            </div>
                            <div class="option-btn" data-value="medium" data-price="200" onclick="selectSimpleOption('size', 'medium', this)">
                                <div class="option-icon">📦</div>
                                <div class="option-name">Medium</div>
                                <div class="option-price">$200</div>
                            </div>
                            <div class="option-btn" data-value="large" data-price="300" onclick="selectSimpleOption('size', 'large', this)">
                                <div class="option-icon">📦</div>
                                <div class="option-name">Large</div>
                                <div class="option-price">$300</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 3: Main Use -->
                    <div class="step-container disabled" id="step-use">
                        <h3>Step 3: Main Purpose</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="gaming" onclick="selectSimpleOption('use', 'gaming', this)">
                                <div class="option-icon">🎮</div>
                                <div class="option-name">Gaming</div>
                            </div>
                            <div class="option-btn" data-value="video_editing" onclick="selectSimpleOption('use', 'video_editing', this)">
                                <div class="option-icon">🎬</div>
                                <div class="option-name">Video Editing</div>
                            </div>
                            <div class="option-btn" data-value="both" onclick="selectSimpleOption('use', 'both', this)">
                                <div class="option-icon">🎯</div>
                                <div class="option-name">Both</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 4: Performance Tier -->
                    <div class="step-container disabled" id="step-tier">
                        <h3>Step 4: Performance Level</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="basic" onclick="selectSimpleOption('tier', 'basic', this)">
                                <div class="option-icon">🥉</div>
                                <div class="option-name">Basic</div>
                            </div>
                            <div class="option-btn" data-value="mid" onclick="selectSimpleOption('tier', 'mid', this)">
                                <div class="option-icon">🥈</div>
                                <div class="option-name">Mid-Range</div>
                            </div>
                            <div class="option-btn" data-value="high" onclick="selectSimpleOption('tier', 'high', this)">
                                <div class="option-icon">🥇</div>
                                <div class="option-name">High-End</div>
                            </div>
                            <div class="option-btn" data-value="flagship" onclick="selectSimpleOption('tier', 'flagship', this)">
                                <div class="option-icon">👑</div>
                                <div class="option-name">Flagship</div>
                            </div>
                        </div>
                    </div>

                    <!-- Step 5: Budget -->
                    <div class="step-container disabled" id="step-budget">
                        <h3>Step 5: Budget Range</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="3500" onclick="selectSimpleOption('budget', '3500', this)">
                                <div class="option-icon">💰</div>
                                <div class="option-name">$3,500</div>
                            </div>
                            <div class="option-btn" data-value="4300" onclick="selectSimpleOption('budget', '4300', this)">
                                <div class="option-icon">💰</div>
                                <div class="option-name">$4,300</div>
                            </div>
                            <div class="option-btn" data-value="5500" onclick="selectSimpleOption('budget', '5500', this)">
                                <div class="option-icon">💰</div>
                                <div class="option-name">$5,500</div>
                            </div>
                            <div class="option-btn" data-value="6999" onclick="selectSimpleOption('budget', '6999', this)">
                                <div class="option-icon">💰</div>
                                <div class="option-name">$6,999</div>
                            </div>
                            <div class="option-btn" data-value="custom" onclick="selectSimpleOption('budget', 'custom', this)">
                                <div class="option-icon">✏️</div>
                                <div class="option-name">Custom</div>
                                <input type="number" id="custom-budget-value" min="3500" max="9999" placeholder="$3500-$9999" style="display: none; margin-top: 10px;" onchange="updateCustomBudget()">
                            </div>
                        </div>
                    </div>

                    <!-- Step 6: Operating System -->
                    <div class="step-container disabled" id="step-system">
                        <h3>Step 6: Operating System</h3>
                        <div class="step-options">
                            <div class="option-btn" data-value="win11_home" onclick="selectSimpleOption('system', 'win11_home', this)">
                                <div class="option-icon">🪟</div>
                                <div class="option-name">Windows 11 Home</div>
                            </div>
                            <div class="option-btn" data-value="win11_pro" onclick="selectSimpleOption('system', 'win11_pro', this)">
                                <div class="option-icon">🪟</div>
                                <div class="option-name">Windows 11 Pro</div>
                            </div>
                        </div>
                    </div>

                    <!-- Base Service Info -->
                    <div class="base-service-info">
                        <h4>📋 Included Base Services ($120)</h4>
                        <ul>
                            <li>System Installation</li>
                            <li>System Updates</li>
                            <li>Driver Installation</li>
                            <li>System Optimization</li>
                            <li>System Stability Testing</li>
                        </ul>
                        <div id="additional-services">
                            <!-- Additional services will be loaded from admin settings -->
                        </div>
                    </div>
                </div>
            </div>

            <!-- Detailed Mode -->
            <div class="pc-mode-section" data-mode="detailed" style="display: none;">
                <div class="detailed-mode-container">
                    <!-- Component Categories -->
                    <div class="component-categories">
                        <div class="category-section" id="cpu-section">
                            <h3>🔧 CPU</h3>
                            <div class="component-options" id="cpu-options">
                                <!-- CPU options will be loaded via JavaScript -->
                            </div>
                        </div>

                        <div class="category-section" id="gpu-section">
                            <h3>🎮 GPU</h3>
                            <div class="component-options" id="gpu-options">
                                <!-- GPU options will be loaded via JavaScript -->
                            </div>
                        </div>

                        <div class="category-section" id="ram-section">
                            <h3>💾 RAM</h3>
                            <div class="component-options" id="ram-options">
                                <!-- RAM options will be loaded via JavaScript -->
                            </div>
                        </div>

                        <div class="category-section" id="storage-section">
                            <h3>💽 Storage</h3>
                            <div class="component-options" id="storage-options">
                                <!-- Storage options will be loaded via JavaScript -->
                            </div>
                        </div>
                    </div>

                    <!-- Selected Components Summary -->
                    <div class="selected-components">
                        <h3>📋 Selected Components</h3>
                        <div class="components-list" id="selected-components-list">
                            <div class="no-selection">No components selected yet</div>
                        </div>
                        <div class="components-total">
                            <strong>Total: <span id="detailed-total">$0.00</span></strong>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Pre-built PCs -->
            <div class="pc-mode-section" data-mode="prebuilt" style="display: none;">
                <div class="pc-component-grid" id="prebuilt-configs">
                    <!-- Pre-built configurations will be loaded via JavaScript -->
                </div>
            </div>

            <div class="order-summary">
                <div class="order-total" id="pc-total">Total: $0.00</div>
                <div class="form-group">
                    <label>
                        <input type="checkbox" id="use-credit-pc"> Use KMS Credit
                    </label>
                </div>
                <div class="form-group">
                    <label for="pc-notes">PC Build Notes:</label>
                    <textarea id="pc-notes" placeholder="Any special requirements..."></textarea>
                </div>
                <button class="btn-primary" id="create-pc-order">Build PC</button>
            </div>
        </div>

        <!-- Service Orders Section -->
        <div class="service-section" id="service-orders">
            <h2>🛠️ Service Orders</h2>

            <!-- Optimize Photo & Video Services -->
            <div class="service-card">
                <h3>📸 Optimize Photo & Video</h3>
                <div id="optimize-services" class="service-grid">
                    <!-- Services will be loaded via JavaScript -->
                </div>
                <div class="order-summary">
                    <div class="order-total" id="optimize-total">Total: $0.00</div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="use-credit-optimize"> Use KMS Credit
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="optimize-notes">Order Notes:</label>
                        <textarea id="optimize-notes" placeholder="Any special instructions..."></textarea>
                    </div>
                    <button class="btn-primary create-order-btn" data-category="optimize">Create Order</button>
                </div>
            </div>

            <!-- Print Services -->
            <div class="service-card">
                <h3>🖨️ Print Services</h3>
                <div id="print-services" class="service-grid">
                    <!-- Services will be loaded via JavaScript -->
                </div>
                <div class="order-summary">
                    <div class="order-total" id="print-total">Total: $0.00</div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="use-credit-print"> Use KMS Credit
                        </label>
                    </div>
                    <div class="form-group">
                        <label for="print-notes">Order Notes:</label>
                        <textarea id="print-notes" placeholder="Any special instructions..."></textarea>
                    </div>
                    <button class="btn-primary create-order-btn" data-category="print">Create Order</button>
                </div>
            </div>
        </div>


    </div>

    <!-- Live Chat Button (Bottom Right) -->
    <div class="live-chat-button">
        <button class="btn-chat" onclick="window.open('../../index.php#live-chat', '_blank')" title="Live Chat with Admin">
            💬
        </button>
    </div>

    <!-- Modals -->
    <?php include 'KMS_Modals/KMS_wallet_modals.php'; ?>
    <?php include 'KMS_Modals/KMS_order_modals.php'; ?>

    <!-- External JavaScript -->
    <script src="KMS_JS/KMS_member.js"></script>
</body>
</html>
