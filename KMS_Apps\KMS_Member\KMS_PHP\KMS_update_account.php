<?php
/**
 * Update Account Information API
 */

header('Content-Type: application/json');
session_start();

// Include required files
$base_path = dirname(dirname(dirname(__DIR__)));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

// Authentication check
if (!isset($_SESSION['loggedin']) || $_SESSION['loggedin'] !== true) {
    echo json_encode(['success' => false, 'message' => 'Please login first']);
    exit;
}

if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    echo json_encode(['success' => false, 'message' => 'Invalid request method']);
    exit;
}

$action = $_POST['action'] ?? '';
$user_id = $_SESSION['id'];

switch ($action) {
    case 'update_nickname':
        updateNickname($user_id);
        break;
    
    default:
        echo json_encode(['success' => false, 'message' => 'Invalid action']);
        break;
}

function updateNickname($user_id) {
    global $link;
    
    $nickname = sanitize_input($_POST['nickname'] ?? '');
    
    // Validation
    if (empty($nickname)) {
        echo json_encode(['success' => false, 'message' => 'Nickname cannot be empty']);
        return;
    }
    
    if (strlen($nickname) > 50) {
        echo json_encode(['success' => false, 'message' => 'Nickname is too long (max 50 characters)']);
        return;
    }
    
    // Check if nickname is already taken by another user
    $check_sql = "SELECT id FROM users WHERE nickname = ? AND id != ?";
    $check_stmt = execute_query($link, $check_sql, "si", [$nickname, $user_id]);
    
    if (!$check_stmt) {
        echo json_encode(['success' => false, 'message' => 'Database error']);
        return;
    }
    
    $check_result = mysqli_stmt_get_result($check_stmt);
    if (mysqli_num_rows($check_result) > 0) {
        mysqli_stmt_close($check_stmt);
        echo json_encode(['success' => false, 'message' => 'Nickname is already taken']);
        return;
    }
    mysqli_stmt_close($check_stmt);
    
    // Update nickname
    $update_sql = "UPDATE users SET nickname = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?";
    $update_stmt = execute_query($link, $update_sql, "si", [$nickname, $user_id]);
    
    if (!$update_stmt) {
        echo json_encode(['success' => false, 'message' => 'Failed to update nickname']);
        return;
    }
    
    mysqli_stmt_close($update_stmt);
    close_db_connection($link);
    
    echo json_encode([
        'success' => true,
        'message' => 'Nickname updated successfully',
        'nickname' => $nickname
    ]);
}
?>
