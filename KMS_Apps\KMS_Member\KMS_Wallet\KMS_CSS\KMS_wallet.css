/* KMS Wallet Styles */

/* Amount Buttons */
.amount-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 15px;
    margin: 15px 0;
}

.amount-btn {
    padding: 15px 10px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.amount-btn:hover {
    transform: translateY(-3px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
    background: linear-gradient(145deg, #34b57e, #267d5a);
}

.amount-btn.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.amount-value {
    font-size: 18px;
    margin-bottom: 5px;
    color: #ffffff;
}

.amount-label {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.8);
}

/* Payment Methods */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(5, 1fr);
    gap: 10px;
    margin: 15px 0;
}

.payment-method {
    display: flex;
    flex-direction: row;
    align-items: center;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.payment-method:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.payment-method.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.payment-icon {
    font-size: 24px;
    margin-right: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.payment-info {
    display: flex;
    flex-direction: column;
    text-align: left;
}

.payment-name {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.8);
}

.payment-desc {
    color: #e0e0e0;
    font-size: 11px;
    margin-top: 2px;
}

/* Custom Amount Input */
.custom-amount {
    margin: 15px 0;
}

.custom-amount input {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background-color: #ffc000;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

.custom-amount input:focus {
    border-color: #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    outline: none;
}

.custom-amount input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Deposit Summary */
.deposit-summary {
    margin: 20px 0;
    padding: 15px;
    border-radius: 10px;
    background: rgba(0, 0, 0, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.1);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin: 5px 0;
    font-size: 16px;
}

/* Copy Code Button */
.copy-code-btn {
    padding: 8px 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
}

.copy-code-btn:hover {
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

/* Withdraw Modal Styles */
#custom-withdraw-amount {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background-color: #ffc000;
    color: white;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    transition: all 0.3s ease;
}

#custom-withdraw-amount:focus {
    border-color: #00ffff;
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
    outline: none;
}

#custom-withdraw-amount::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

/* Withdraw Commission Modal */
.withdraw-commission-modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    padding: 20px;
    border-radius: 10px;
    z-index: 1000;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.5);
    max-width: 400px;
    width: 90%;
    text-align: center;
}

.withdraw-commission-modal h3 {
    color: #00ffff;
    margin-bottom: 15px;
}

.withdraw-commission-modal p {
    margin-bottom: 20px;
    color: white;
}

.withdraw-commission-modal button {
    padding: 10px 20px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
}

.withdraw-commission-modal button:hover {
    background: linear-gradient(145deg, #34b57e, #267d5a);
}

/* Withdraw Commission and Transfer Buttons */
.withdraw-commission-btn,
.transfer-commission-btn {
    padding: 10px 20px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 16px;
    transition: all 0.3s ease;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.withdraw-commission-btn:hover,
.transfer-commission-btn:hover {
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

/* PC Builder Simple Mode Styles */
.simple-mode-steps {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.step-container {
    padding: 20px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(43, 152, 105, 0.8), rgba(30, 107, 74, 0.8));
    transition: all 0.3s ease;
}

.step-container.disabled {
    opacity: 0.5;
    pointer-events: none;
}

.step-container h3 {
    color: #00ffff;
    margin-bottom: 15px;
    text-align: center;
}

.step-options {
    display: flex;
    flex-wrap: wrap;
    gap: 15px;
    justify-content: center;
}

.option-btn {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px 20px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 10px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 120px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.option-btn:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.option-btn.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.option-icon {
    font-size: 32px;
    margin-bottom: 8px;
}

.option-name {
    color: white;
    font-weight: bold;
    font-size: 14px;
    text-align: center;
    margin-bottom: 5px;
}

.option-price {
    color: #ffd700;
    font-size: 12px;
    font-weight: bold;
}

.base-service-info {
    margin-top: 30px;
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(255, 215, 0, 0.1), rgba(255, 215, 0, 0.05));
}

.base-service-info h4 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.base-service-info ul {
    list-style: none;
    padding: 0;
    margin: 0;
}

.base-service-info li {
    color: white;
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.base-service-info li:before {
    content: "✓ ";
    color: #00ff00;
    font-weight: bold;
    margin-right: 8px;
}

/* PC Builder Detailed Mode Styles */
.detailed-mode-container {
    display: flex;
    flex-direction: column;
    gap: 30px;
}

.component-categories {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.category-section {
    padding: 20px;
    border: 2px solid rgba(0, 255, 255, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(43, 152, 105, 0.8), rgba(30, 107, 74, 0.8));
}

.category-section h3 {
    color: #00ffff;
    margin-bottom: 15px;
    text-align: center;
}

.component-options {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
    justify-content: center;
}

.component-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 10px 15px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    background: linear-gradient(145deg, #2b9869, #1e6b4a);
    cursor: pointer;
    transition: all 0.3s ease;
    width: 150px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
}

.component-option:hover {
    border-color: #ffd700;
    background: linear-gradient(145deg, #34b57e, #267d5a);
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.4);
}

.component-option.selected {
    border-color: #00ffff;
    background: linear-gradient(145deg, #05c3b6, #04a89d);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.component-name {
    color: white;
    font-weight: bold;
    font-size: 13px;
    text-align: center;
    margin-bottom: 5px;
}

.component-specs {
    color: #e0e0e0;
    font-size: 11px;
    text-align: center;
    margin-bottom: 5px;
}

.component-price {
    color: #ffd700;
    font-size: 12px;
    font-weight: bold;
}

.selected-components {
    padding: 20px;
    border: 2px solid rgba(255, 215, 0, 0.3);
    border-radius: 10px;
    background: linear-gradient(145deg, rgba(0, 0, 0, 0.3), rgba(0, 0, 0, 0.1));
}

.selected-components h3 {
    color: #ffd700;
    margin-bottom: 15px;
    text-align: center;
}

.components-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.component-item {
    display: flex;
    justify-content: space-between;
    padding: 10px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.component-item-name {
    color: white;
    font-weight: bold;
}

.component-item-price {
    color: #ffd700;
}

.no-selection {
    color: #e0e0e0;
    text-align: center;
    font-style: italic;
    padding: 20px;
}

.components-total {
    margin-top: 20px;
    padding: 10px;
    border-top: 2px solid rgba(255, 255, 255, 0.2);
    text-align: right;
    color: #00ff00;
    font-size: 18px;
}
