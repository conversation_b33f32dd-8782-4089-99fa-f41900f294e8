<?php
header('Content-Type: application/json');
$base_path = dirname(dirname(dirname(dirname(__DIR__))));
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Config' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_config.php';
require_once $base_path . DIRECTORY_SEPARATOR . 'KMS_Apps' . DIRECTORY_SEPARATOR . 'KMS_Core' . DIRECTORY_SEPARATOR . 'KMS_Functions' . DIRECTORY_SEPARATOR . 'KMS_PHP' . DIRECTORY_SEPARATOR . 'KMS_functions.php';

if ($_SERVER["REQUEST_METHOD"] == "POST") {
    $username = sanitize_input($_POST['username']);
    $password = $_POST['password'];

    // Basic validation
    if (empty($username) || empty($password)) {
        echo json_encode([
            'success' => false,
            'message' => 'Please enter both username and password.'
        ]);
        exit;
    }

    // User login - check database for both regular users and admin
    $sql = "SELECT id, username, password, language, is_admin FROM users WHERE username = ? AND is_active = 1";
    $stmt = execute_query($link, $sql, "s", [$username]);

    if ($stmt) {
        $result = mysqli_stmt_get_result($stmt);
        $user = mysqli_fetch_assoc($result);
        mysqli_stmt_close($stmt);

        if ($user && password_verify($password, $user['password'])) {
            // Update last login
            $update_sql = "UPDATE users SET last_login = CURRENT_TIMESTAMP WHERE id = ?";
            $update_stmt = execute_query($link, $update_sql, "i", [$user['id']]);
            if ($update_stmt) {
                mysqli_stmt_close($update_stmt);
            }

            // Set session variables
            $_SESSION["loggedin"] = true;
            $_SESSION["id"] = $user['id'];
            $_SESSION["username"] = $user['username'];
            $_SESSION["is_admin"] = (bool)$user['is_admin']; // Convert to boolean
            $_SESSION["language"] = $user['language'];

            echo json_encode([
                'success' => true,
                'is_admin' => (bool)$user['is_admin'],
                'message' => $user['is_admin'] ? 'Admin login successful!' : 'Login successful!'
            ]);
        } else {
            echo json_encode([
                'success' => false,
                'message' => 'Invalid username or password.'
            ]);
        }
    } else {
        echo json_encode([
            'success' => false,
            'message' => 'Login failed. Please try again later.'
        ]);
    }

    close_db_connection($link);
} else {
    echo json_encode([
        'success' => false,
        'message' => 'Invalid request method.'
    ]);
}
?>