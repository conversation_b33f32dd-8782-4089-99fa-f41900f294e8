/* KMS Member Dashboard Styles */
body {
    font-family: Arial, sans-serif;
    background-color: #a48f19;
    color: white;
    margin: 0;
    padding: 20px;
    position: relative;
}

.container {
    max-width: 1000px;
    margin: auto;
    background-color: #2b9869;
    padding: 20px;
    border-radius: 14px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.5);
}

/* Top Controls */
.top-controls {
    position: fixed;
    top: 10px;
    right: 10px;
    display: flex;
    gap: 10px;
    z-index: 1000;
}

/* Live Chat Button */
.live-chat-button {
    position: fixed;
    bottom: 20px;
    right: 20px;
    z-index: 1000;
}

.btn-chat {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    background-color: #00bcaa;
    color: white;
    border: none;
    font-size: 24px;
    cursor: pointer;
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.btn-chat:hover {
    background-color: #ffbf00;
    transform: scale(1.1);
}

h1, h2 { 
    color: #00ffff; 
    text-align: center; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8);
}

h3, h4, h5, h6 { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

p, div, span { 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.6); 
}

#welcome-msg { 
    text-align: center; 
    font-size: 24px; 
    margin-bottom: 20px; 
    text-shadow: 2px 2px 4px rgba(0,0,0,0.8); 
}

/* Wallet Section Styles */
.wallet-section { 
    margin: 20px 0; 
}

.wallet-card { 
    background: rgba(19,73,240,0.4); 
    padding: 10px; 
    border-radius: 10px; 
    margin-bottom: 20px; 
}

.wallet-balance { 
    text-align: center; 
    margin-bottom: 20px; 
}

.balance-amount { 
    font-size: 36px; 
    font-weight: bold; 
    color: #ffd700; 
    margin: 10px 0; 
}

.wallet-stats { 
    display: flex; 
    justify-content: space-around; 
    margin: 20px 0; 
    flex-wrap: wrap; 
    gap: 15px; 
}

.stat-item { 
    text-align: center; 
    flex: 1; 
    min-width: 150px; 
}

.stat-item h4 { 
    color: #00ffff; 
    margin: 0 0 10px 0; 
    font-size: 14px; 
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8); 
}

.stat-value {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: linear-gradient(145deg, rgba(255,215,0,0.1), rgba(255,215,0,0.05));
    border: 2px solid rgba(255,215,0,0.3);
    border-radius: 10px;
    padding: 15px 10px;
    margin: 5px 0;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        inset 0 1px 0 rgba(255,255,255,0.1),
        inset 0 -1px 0 rgba(0,0,0,0.2);
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.stat-value::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.1), transparent);
    transition: left 0.5s;
}

.stat-value:hover {
    transform: translateY(-2px);
    box-shadow:
        0 6px 12px rgba(0,0,0,0.4),
        inset 0 1px 0 rgba(255,255,255,0.2),
        inset 0 -1px 0 rgba(0,0,0,0.3);
}

.stat-value:hover::before {
    left: 100%;
}

.wallet-actions { 
    display: flex; 
    justify-content: center; 
    gap: 15px; 
    margin-top: 20px; 
}

/* Button Styles */
button {
    cursor: pointer;
    padding: 10px 15px;
    border: none;
    border-radius: 10px;
    font-weight: bold;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.8);
    box-shadow: 0 2px 4px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

button:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 8px rgba(0,0,0,0.4);
}

.btn-deposit {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-withdraw {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

.btn-transfer {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-primary {
    background: linear-gradient(145deg, #2196F3, #0b7dda);
    color: white;
}

.btn-success {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
}

.btn-warning {
    background: linear-gradient(145deg, #ff9800, #e68900);
    color: white;
}

.btn-danger {
    background: linear-gradient(145deg, #f44336, #da190b);
    color: white;
}

/* Service Section Styles */
.service-section {
    margin: 30px 0;
    background: rgba(0,0,0,0.2);
    padding: 20px;
    border-radius: 10px;
}

.service-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.service-card {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.2);
    transition: all 0.3s ease;
}

.service-card:hover {
    background: rgba(255,255,255,0.15);
    border-color: rgba(255,255,255,0.4);
    transform: translateY(-5px);
}

.service-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.service-item:last-child {
    border-bottom: none;
}

.service-name {
    font-weight: bold;
    color: #00ffff;
}

.service-price {
    color: #ffd700;
    font-weight: bold;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-top: 10px;
}

.quantity-btn {
    width: 30px;
    height: 30px;
    border-radius: 50%;
    background: #2196F3;
    color: white;
    border: none;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-input {
    width: 60px;
    text-align: center;
    padding: 5px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255,255,255,0.9);
    color: #333;
}

/* Order Summary Styles */
.order-summary {
    background: rgba(0,0,0,0.3);
    padding: 20px;
    border-radius: 10px;
    margin-top: 20px;
}

.order-total {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    text-align: center;
    margin: 20px 0;
}

/* PC Builder Styles */
.pc-builder-section {
    margin: 30px 0;
}

.mode-selector {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 30px;
}

.mode-btn {
    padding: 15px 30px;
    font-size: 16px;
    font-weight: bold;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mode-btn.active {
    background: linear-gradient(145deg, #4CAF50, #45a049);
    color: white;
    transform: scale(1.05);
}

.mode-btn:not(.active) {
    background: rgba(255,255,255,0.2);
    color: #ccc;
}

.pc-component-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 20px;
    margin-top: 20px;
}

.component-card {
    background: rgba(255,255,255,0.1);
    padding: 15px;
    border-radius: 10px;
    border: 2px solid rgba(255,255,255,0.2);
}

.component-select {
    width: 100%;
    padding: 10px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: rgba(255,255,255,0.9);
    color: #333;
    margin-top: 10px;
}

/* Enhanced PC Builder Styles */
.pc-option-card, .prebuilt-config-card {
    background: rgba(0, 188, 170, 0.2);
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.pc-option-card:hover, .prebuilt-config-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 16px rgba(0, 0, 0, 0.3);
}

.pc-option-card.selected, .prebuilt-config-card.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.2);
    box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
}

.pc-option-card h4, .prebuilt-config-card h4 {
    color: #00ffff;
    margin-top: 0;
    font-size: 18px;
}

.price-range, .config-price {
    font-size: 20px;
    font-weight: bold;
    color: #ffbf00;
    margin: 10px 0;
}

.specs-list, .config-specs {
    list-style-type: none;
    padding-left: 0;
    margin: 15px 0;
}

.specs-list li, .config-specs li {
    padding: 5px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.specs-list li:last-child, .config-specs li:last-child {
    border-bottom: none;
}

.color-option, .config-description {
    margin: 10px 0;
    font-style: italic;
}

/* Component Category Styles */
.component-category {
    margin-bottom: 30px;
}

.component-category h4 {
    color: #00ffff;
    border-bottom: 2px solid #00bcaa;
    padding-bottom: 5px;
    margin-bottom: 15px;
}

.component-options {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
}

.component-option {
    background: rgba(0, 0, 0, 0.3);
    border-radius: 8px;
    padding: 12px;
    transition: all 0.3s ease;
    border: 2px solid transparent;
    cursor: pointer;
}

.component-option:hover {
    background: rgba(0, 0, 0, 0.4);
}

.component-option.selected {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.1);
}

.component-name {
    font-weight: bold;
    color: white;
    margin-bottom: 5px;
}

.component-price {
    color: #ffbf00;
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 5px;
}

.component-description {
    color: #ccc;
    font-size: 14px;
    margin-bottom: 10px;
}

.select-option-btn, .select-component-btn, .select-config-btn {
    width: 100%;
    margin-top: 10px;
}

/* Affiliate Section Styles */
.affiliate-section {
    margin: 30px 0;
}

.affiliate-code-display {
    background: rgba(0,0,0,0.3);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
    margin: 20px 0;
}

.affiliate-code {
    font-size: 24px;
    font-weight: bold;
    color: #ffd700;
    background: rgba(255,215,0,0.1);
    padding: 10px 20px;
    border-radius: 5px;
    display: inline-block;
    margin: 10px 0;
}

.referral-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin: 20px 0;
}

.referral-stat-card {
    background: rgba(255,255,255,0.1);
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

/* Modal Styles */
.modal {
    display: none;
    position: fixed;
    z-index: 1000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
}

.modal-content {
    background-color: #2b9869;
    margin: 5% auto;
    padding: 20px;
    border-radius: 10px;
    width: 90%;
    max-width: 500px;
    position: relative;
}

.close {
    color: #aaa;
    float: right;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
}

.close:hover {
    color: white;
}

/* Form Styles */
.form-group {
    margin: 15px 0;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    color: #00ffff;
    font-weight: bold;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 10px;
    border: 1px solid #ccc;
    border-radius: 5px;
    background: rgba(255,255,255,0.9);
    color: #333;
    box-sizing: border-box;
}

.form-group textarea {
    background-color: #ffc000;
    resize: vertical;
    min-height: 100px;
}

/* Top Control Buttons */
.btn-settings, .btn-logout {
    padding: 8px 16px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 14px;
    font-weight: bold;
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-block;
}

.btn-settings {
    background-color: #00bcaa;
    color: white;
}

.btn-settings:hover {
    background-color: #ffbf00;
}

.btn-logout {
    background-color: #e74c3c;
    color: white;
}

.btn-logout:hover {
    background-color: #c0392b;
}

/* Affiliate Actions */
.affiliate-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
    justify-content: center;
}

.btn-secondary {
    background-color: #6c757d;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background-color: #5a6268;
}

/* Scroll to section function */
html {
    scroll-behavior: smooth;
}

/* Notification System */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 20px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    z-index: 10000;
    max-width: 400px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    display: flex;
    justify-content: space-between;
    align-items: center;
    animation: slideIn 0.3s ease-out;
}

.notification-success {
    background-color: #4CAF50;
}

.notification-error {
    background-color: #f44336;
}

.notification-info {
    background-color: #2196F3;
}

.notification-warning {
    background-color: #ff9800;
}

.notification-close {
    background: none;
    border: none;
    color: white;
    font-size: 20px;
    cursor: pointer;
    margin-left: 10px;
}

.notification-close:hover {
    opacity: 0.7;
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

/* Payment Method Buttons */
.payment-methods {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 10px;
    margin: 15px 0;
}

.payment-method-btn {
    padding: 12px 16px;
    border: 2px solid #ccc;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: bold;
}

.payment-method-btn:hover {
    border-color: #00bcaa;
    background: rgba(0, 188, 170, 0.2);
}

.payment-method-btn.active {
    border-color: #00ffff;
    background: rgba(0, 255, 255, 0.3);
    box-shadow: 0 0 10px rgba(0, 255, 255, 0.5);
}

/* Modal Enhancements */
.modal-actions {
    display: flex;
    gap: 10px;
    justify-content: center;
    margin-top: 20px;
}

.withdraw-info {
    background: rgba(0, 0, 0, 0.2);
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.withdraw-info p {
    margin: 5px 0;
}

/* Responsive Design */
@media (max-width: 768px) {
    .container {
        padding: 15px;
        margin: 10px;
    }
    
    .wallet-stats {
        flex-direction: column;
    }
    
    .service-grid {
        grid-template-columns: 1fr;
    }
    
    .mode-selector {
        flex-direction: column;
        align-items: center;
    }
    
    .pc-component-grid {
        grid-template-columns: 1fr;
    }
}

/* Scrollbar Styles */
::-webkit-scrollbar {
    width: 3px;
}

::-webkit-scrollbar-track {
    background: rgba(255,255,255,0.1);
}

::-webkit-scrollbar-thumb {
    background: #2b9869;
    border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
    background: #1e6b4a;
}
